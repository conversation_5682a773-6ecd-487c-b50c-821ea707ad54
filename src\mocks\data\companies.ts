// Company mock data for MSW
export interface Company {
  id: string;
  name: string;
  logo: string;
  domain: string;
  isActive: boolean;
  settings: {
    currency: string;
    timezone: string;
    language: string;
    dateFormat: string;
  };
  createdAt?: string;
  updatedAt?: string;
  description?: string;
  industry?: string;
  size?: 'small' | 'medium' | 'large' | 'enterprise';
  location?: {
    country: string;
    city: string;
    address?: string;
  };
  contact?: {
    email: string;
    phone: string;
    website?: string;
  };
}

export const companies: Company[] = [
  {
    id: 'company-1',
    name: 'Acme Corporation',
    logo: '🏢',
    domain: 'acme.com',
    isActive: true,
    description: 'Leading provider of innovative business solutions',
    industry: 'Technology',
    size: 'large',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    settings: {
      currency: 'USD',
      timezone: 'America/New_York',
      language: 'en',
      dateFormat: 'MM/dd/yyyy',
    },
    location: {
      country: 'United States',
      city: 'New York',
      address: '123 Business Ave, New York, NY 10001',
    },
    contact: {
      email: '<EMAIL>',
      phone: '******-0123',
      website: 'https://acme.com',
    },
  },
  {
    id: 'company-2',
    name: 'TechStart Inc.',
    logo: '🚀',
    domain: 'techstart.com',
    isActive: true,
    description: 'Innovative startup focused on cutting-edge technology',
    industry: 'Software',
    size: 'medium',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-16T14:20:00Z',
    settings: {
      currency: 'EUR',
      timezone: 'Europe/London',
      language: 'en',
      dateFormat: 'dd/MM/yyyy',
    },
    location: {
      country: 'United Kingdom',
      city: 'London',
      address: '456 Tech Street, London, EC1A 1BB',
    },
    contact: {
      email: '<EMAIL>',
      phone: '+44-20-7946-0958',
      website: 'https://techstart.com',
    },
  },
  {
    id: 'company-3',
    name: 'Global Solutions Ltd.',
    logo: '🌍',
    domain: 'globalsolutions.com',
    isActive: true,
    description: 'Worldwide consulting and business solutions provider',
    industry: 'Consulting',
    size: 'enterprise',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-17T09:15:00Z',
    settings: {
      currency: 'GBP',
      timezone: 'Europe/London',
      language: 'en',
      dateFormat: 'dd/MM/yyyy',
    },
    location: {
      country: 'United Kingdom',
      city: 'Manchester',
      address: '789 Global Plaza, Manchester, M1 1AA',
    },
    contact: {
      email: '<EMAIL>',
      phone: '+44-************',
      website: 'https://globalsolutions.com',
    },
  },
  {
    id: 'company-4',
    name: 'Innovation Labs',
    logo: '🔬',
    domain: 'innovationlabs.com',
    isActive: true,
    description: 'Research and development company specializing in emerging technologies',
    industry: 'Research & Development',
    size: 'small',
    createdAt: '2024-01-04T00:00:00Z',
    updatedAt: '2024-01-18T11:45:00Z',
    settings: {
      currency: 'USD',
      timezone: 'America/Los_Angeles',
      language: 'en',
      dateFormat: 'MM/dd/yyyy',
    },
    location: {
      country: 'United States',
      city: 'San Francisco',
      address: '321 Innovation Drive, San Francisco, CA 94105',
    },
    contact: {
      email: '<EMAIL>',
      phone: '******-555-0199',
      website: 'https://innovationlabs.com',
    },
  },
  {
    id: 'company-5',
    name: 'Digital Dynamics',
    logo: '💻',
    domain: 'digitaldynamics.com',
    isActive: false,
    description: 'Digital transformation and automation specialists',
    industry: 'Digital Services',
    size: 'medium',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-19T08:30:00Z',
    settings: {
      currency: 'CAD',
      timezone: 'America/Toronto',
      language: 'en',
      dateFormat: 'yyyy-MM-dd',
    },
    location: {
      country: 'Canada',
      city: 'Toronto',
      address: '654 Digital Way, Toronto, ON M5V 3A8',
    },
    contact: {
      email: '<EMAIL>',
      phone: '******-555-0177',
      website: 'https://digitaldynamics.com',
    },
  },
];

// Helper functions
export const getCompanyById = (id: string): Company | undefined => {
  return companies.find(company => company.id === id);
};

export const getActiveCompanies = (): Company[] => {
  return companies.filter(company => company.isActive);
};

export const getCompaniesByIndustry = (industry: string): Company[] => {
  return companies.filter(company => company.industry === industry);
};

export const getCompaniesBySize = (size: Company['size']): Company[] => {
  return companies.filter(company => company.size === size);
};

export const searchCompanies = (query: string): Company[] => {
  const lowercaseQuery = query.toLowerCase();
  return companies.filter(
    company =>
      company.name.toLowerCase().includes(lowercaseQuery) ||
      company.domain.toLowerCase().includes(lowercaseQuery) ||
      company.description?.toLowerCase().includes(lowercaseQuery) ||
      company.industry?.toLowerCase().includes(lowercaseQuery)
  );
};
