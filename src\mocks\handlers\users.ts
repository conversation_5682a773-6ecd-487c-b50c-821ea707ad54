// User management handlers for MSW
import { http, HttpResponse } from 'msw';
import { 
  users, 
  devUsers, 
  allUsers, 
  getUserById,
  type User 
} from '../data/users';
import { 
  delay, 
  randomDelay, 
  simulateError, 
  commonErrors,
  successResponse,
  errorResponse,
  notFoundResponse,
  unauthorizedResponse,
  extractBearerToken,
  validateToken,
  paginate,
  fuzzySearch,
  sortData,
  filterData,
  parseQueryParams,
  generateId,
  generateTimestamp,
  type SortDirection,
  type FilterCondition
} from '../utils';

// Mutable copy of users for CRUD operations
let mutableUsers = [...allUsers];

// Helper to check permissions
const hasPermission = (user: User, permission: string): boolean => {
  return user.permissions.includes(permission) || user.permissions.includes('admin');
};

// Helper to get authenticated user
const getAuthenticatedUser = async (authHeader: string | null): Promise<User | null> => {
  const token = extractBearerToken(authHeader);
  if (!token) return null;
  
  const userId = validateToken(token);
  if (!userId) return null;
  
  return getUserById(userId) || null;
};

export const userHandlers = [
  // Get all users with pagination, search, and filtering
  http.get('/api/users', async ({ request }) => {
    await randomDelay(300, 800);
    
    // Simulate occasional errors
    const error = simulateError([commonErrors.serverError]);
    if (error) return error;
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const url = new URL(request.url);
    const params = parseQueryParams(url);
    
    let filteredUsers = [...mutableUsers];
    
    // Apply search
    if (params.search) {
      filteredUsers = fuzzySearch(filteredUsers, params.search, ['name', 'email', 'role']);
    }
    
    // Apply filters
    const filters: FilterCondition<User>[] = [];
    if (params.role) {
      filters.push({ field: 'role', operator: 'eq', value: params.role });
    }
    if (params.status) {
      filters.push({ field: 'status', operator: 'eq', value: params.status });
    }
    if (filters.length > 0) {
      filteredUsers = filterData(filteredUsers, filters);
    }
    
    // Apply sorting
    if (params.sortBy) {
      const direction: SortDirection = params.sortOrder === 'desc' ? 'desc' : 'asc';
      filteredUsers = sortData(filteredUsers, params.sortBy as keyof User, direction);
    }
    
    // Apply pagination
    const paginatedResult = paginate(filteredUsers, {
      page: params.page,
      limit: params.limit,
    });
    
    return successResponse(paginatedResult);
  }),

  // Get single user by ID
  http.get('/api/users/:id', async ({ params, request }) => {
    await delay(200);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const user = mutableUsers.find(u => u.id === params.id);
    if (!user) {
      return notFoundResponse('User');
    }
    
    return successResponse(user);
  }),

  // Create new user
  http.post('/api/users', async ({ request }) => {
    await randomDelay(800, 1200);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'write') && !hasPermission(currentUser, 'manage_users')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    try {
      const userData = await request.json() as Partial<User>;
      
      // Validate required fields
      if (!userData.name || !userData.email) {
        return errorResponse('Name and email are required', 400);
      }
      
      // Check if email already exists
      if (mutableUsers.some(u => u.email === userData.email)) {
        return errorResponse('Email already exists', 409);
      }
      
      const newUser: User = {
        id: generateId(),
        name: userData.name,
        email: userData.email,
        role: userData.role || 'user',
        avatar: userData.avatar || '👤',
        permissions: userData.permissions || ['read', 'write'],
        status: userData.status || 'active',
        createdAt: generateTimestamp(),
        preferences: userData.preferences || {
          theme: 'system',
          language: 'en',
          notifications: true,
        },
      };
      
      mutableUsers.push(newUser);
      
      return successResponse(newUser, 201);
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Update user
  http.put('/api/users/:id', async ({ params, request }) => {
    await delay(600);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    const userIndex = mutableUsers.findIndex(u => u.id === params.id);
    if (userIndex === -1) {
      return notFoundResponse('User');
    }
    
    const targetUser = mutableUsers[userIndex];
    
    // Check permissions - users can update themselves, or need manage_users permission
    if (currentUser.id !== targetUser.id && !hasPermission(currentUser, 'manage_users')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    try {
      const updateData = await request.json() as Partial<User>;
      
      // Prevent email conflicts
      if (updateData.email && updateData.email !== targetUser.email) {
        if (mutableUsers.some(u => u.email === updateData.email && u.id !== targetUser.id)) {
          return errorResponse('Email already exists', 409);
        }
      }
      
      // Prevent role escalation unless user has admin permissions
      if (updateData.role && updateData.role !== targetUser.role) {
        if (!hasPermission(currentUser, 'admin')) {
          return HttpResponse.json({ error: 'Cannot change user role' }, { status: 403 });
        }
      }
      
      const updatedUser: User = {
        ...targetUser,
        ...updateData,
        id: targetUser.id, // Prevent ID changes
        createdAt: targetUser.createdAt, // Prevent creation date changes
        updatedAt: generateTimestamp(),
      };
      
      mutableUsers[userIndex] = updatedUser;
      
      return successResponse(updatedUser);
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Delete user
  http.delete('/api/users/:id', async ({ params, request }) => {
    await delay(400);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'delete') && !hasPermission(currentUser, 'manage_users')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const userIndex = mutableUsers.findIndex(u => u.id === params.id);
    if (userIndex === -1) {
      return notFoundResponse('User');
    }
    
    // Prevent self-deletion
    if (mutableUsers[userIndex].id === currentUser.id) {
      return errorResponse('Cannot delete your own account', 400);
    }
    
    mutableUsers.splice(userIndex, 1);
    
    return successResponse({ message: 'User deleted successfully' });
  }),

  // Bulk operations
  http.post('/api/users/bulk', async ({ request }) => {
    await delay(1000);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'manage_users')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    try {
      const { action, userIds } = await request.json() as {
        action: 'activate' | 'deactivate' | 'delete';
        userIds: string[];
      };
      
      if (!action || !Array.isArray(userIds)) {
        return errorResponse('Action and userIds are required', 400);
      }
      
      let affectedCount = 0;
      
      switch (action) {
        case 'activate':
        case 'deactivate':
          userIds.forEach(id => {
            const userIndex = mutableUsers.findIndex(u => u.id === id);
            if (userIndex !== -1 && mutableUsers[userIndex].id !== currentUser.id) {
              mutableUsers[userIndex].status = action === 'activate' ? 'active' : 'inactive';
              affectedCount++;
            }
          });
          break;
          
        case 'delete':
          userIds.forEach(id => {
            const userIndex = mutableUsers.findIndex(u => u.id === id);
            if (userIndex !== -1 && mutableUsers[userIndex].id !== currentUser.id) {
              mutableUsers.splice(userIndex, 1);
              affectedCount++;
            }
          });
          break;
      }
      
      return successResponse({
        message: `${action} completed`,
        affectedCount,
      });
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),
];
