import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import DynamicAppHeader from './DynamicAppHeader';
import type { DynamicAppHeaderProps } from './DynamicAppHeader';

// Mock the theme store
vi.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      surface: '#ffffff',
      border: '#e5e7eb',
      text: '#111827',
      mutedForeground: '#6b7280',
      primary: '#3b82f6',
      primaryForeground: '#ffffff',
      background: '#f9fafb',
      hover: '#f3f4f6',
      muted: '#f9fafb',
      error: '#ef4444',
      errorForeground: '#ffffff',
    },
  }),
}));

// Sample test data
const mockAppData = {
  name: 'Test App',
  icon: <div data-testid="app-icon">Icon</div>,
  navLinks: [
    { label: 'Home', href: '/home', isActive: true },
    { label: 'Products', href: '/products', isActive: false },
    { label: 'Orders', href: '/orders', isActive: false },
  ],
};

const mockUserData = {
  name: 'John Doe',
  avatar: <div data-testid="user-avatar">Avatar</div>,
  notifications: [
    { count: 3, icon: <div data-testid="notification-icon">Bell</div> },
  ],
};

const mockViewData = {
  title: 'Test View',
  actions: [
    { label: 'New', onClick: vi.fn(), isPrimary: true },
    { label: 'Upload', onClick: vi.fn(), isPrimary: false },
  ],
  search: {
    filters: [
      { id: 'filter1', label: 'Active' },
      { id: 'filter2', label: 'Pending' },
    ],
    onSearch: vi.fn(),
    onRemoveFilter: vi.fn(),
  },
  pagination: {
    currentRange: '1-10 / 50',
    onNext: vi.fn(),
    onPrev: vi.fn(),
  },
  viewModes: [
    { name: 'List', icon: <div data-testid="list-icon">List</div> },
    { name: 'Grid', icon: <div data-testid="grid-icon">Grid</div> },
  ],
  activeViewMode: 'List',
};

const defaultProps: DynamicAppHeaderProps = {
  app: mockAppData,
  user: mockUserData,
  view: mockViewData,
};

// Helper function to render component with Router context
const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <MemoryRouter>
      {component}
    </MemoryRouter>
  );
};

describe('DynamicAppHeader', () => {
  it('renders the app name and icon', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByText('Test App')).toBeInTheDocument();
    expect(screen.getByTestId('app-icon')).toBeInTheDocument();
  });

  it('renders navigation links with active state', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Products')).toBeInTheDocument();
    expect(screen.getByText('Orders')).toBeInTheDocument();
  });

  it('renders user information', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByTestId('user-avatar')).toBeInTheDocument();
  });

  it('renders notification badge with count', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByTestId('notification-icon')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('renders view title and action buttons', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByText('Test View')).toBeInTheDocument();
    expect(screen.getByText('New')).toBeInTheDocument();
    expect(screen.getByText('Upload')).toBeInTheDocument();
  });

  it('renders search input and filters', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
  });

  it('renders pagination controls', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByText('1-10 / 50')).toBeInTheDocument();
    expect(screen.getByLabelText('Previous page')).toBeInTheDocument();
    expect(screen.getByLabelText('Next page')).toBeInTheDocument();
  });

  it('renders view mode switcher', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    expect(screen.getByTestId('list-icon')).toBeInTheDocument();
    expect(screen.getByTestId('grid-icon')).toBeInTheDocument();
  });

  it('calls action handlers when buttons are clicked', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    fireEvent.click(screen.getByText('New'));
    expect(mockViewData.actions[0].onClick).toHaveBeenCalled();

    fireEvent.click(screen.getByText('Upload'));
    expect(mockViewData.actions[1].onClick).toHaveBeenCalled();
  });

  it('calls pagination handlers when navigation buttons are clicked', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    fireEvent.click(screen.getByLabelText('Previous page'));
    expect(mockViewData.pagination.onPrev).toHaveBeenCalled();

    fireEvent.click(screen.getByLabelText('Next page'));
    expect(mockViewData.pagination.onNext).toHaveBeenCalled();
  });

  it('handles search input and submission', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchInput, { target: { value: 'test query' } });
    fireEvent.submit(searchInput.closest('form')!);

    expect(mockViewData.search.onSearch).toHaveBeenCalledWith('test query');
  });

  it('handles filter removal', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    const removeButtons = screen.getAllByLabelText(/Remove .* filter/);
    fireEvent.click(removeButtons[0]);

    expect(mockViewData.search.onRemoveFilter).toHaveBeenCalledWith('filter1');
  });

  it('toggles mobile menu when hamburger button is clicked', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} />);

    const menuButton = screen.getByLabelText('Toggle mobile menu');
    fireEvent.click(menuButton);

    // Check if mobile menu items are visible
    const mobileNavLinks = screen.getAllByText('Home');
    expect(mobileNavLinks.length).toBeGreaterThan(1); // Desktop + mobile versions
  });

  it('handles notification count display correctly', () => {
    const propsWithHighCount = {
      ...defaultProps,
      user: {
        ...mockUserData,
        notifications: [
          { count: 150, icon: <div data-testid="notification-icon">Bell</div> },
        ],
      },
    };

    renderWithRouter(<DynamicAppHeader {...propsWithHighCount} />);
    expect(screen.getByText('99+')).toBeInTheDocument();
  });

  it('renders with custom test id', () => {
    renderWithRouter(<DynamicAppHeader {...defaultProps} data-testid="custom-header" />);
    expect(screen.getByTestId('custom-header')).toBeInTheDocument();
  });
});
