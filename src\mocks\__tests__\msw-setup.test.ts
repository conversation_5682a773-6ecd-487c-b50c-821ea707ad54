// Test file to validate MSW setup and functionality
import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { setupServer } from 'msw/node';
import { handlers } from '../handlers';
import { applyScenario } from '../scenarios';

// Setup MSW server for testing
const server = setupServer(...handlers);

// Start server before all tests
beforeAll(() => {
  server.listen({ onUnhandledRequest: 'error' });
});

// Reset handlers after each test
beforeEach(() => {
  server.resetHandlers();
  applyScenario('fastTesting');
});

// Close server after all tests
afterAll(() => {
  server.close();
});

describe('MSW Infrastructure', () => {
  describe('Authentication Endpoints', () => {
    it('should handle login successfully', async () => {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'dev',
        }),
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data).toHaveProperty('user');
      expect(data).toHaveProperty('token');
      expect(data.user.email).toBe('<EMAIL>');
    });

    it('should reject invalid credentials', async () => {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'wrongpassword',
        }),
      });

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    it('should return current user with valid token', async () => {
      const token = 'mock-jwt-token-dev-admin';
      const response = await fetch('/api/auth/me', {
        headers: { Authorization: `Bearer ${token}` },
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data).toHaveProperty('user');
      expect(data.user.id).toBe('dev-admin');
    });
  });

  describe('User Management Endpoints', () => {
    const authToken = 'mock-jwt-token-dev-admin';
    const authHeaders = { Authorization: `Bearer ${authToken}` };

    it('should fetch users list', async () => {
      const response = await fetch('/api/users', {
        headers: authHeaders,
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data).toHaveProperty('data');
      expect(data).toHaveProperty('pagination');
      expect(Array.isArray(data.data)).toBe(true);
    });

    it('should create a new user', async () => {
      const newUser = {
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
      };

      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { ...authHeaders, 'Content-Type': 'application/json' },
        body: JSON.stringify(newUser),
      });

      expect(response.status).toBe(201);
      const data = await response.json();
      expect(data.name).toBe(newUser.name);
      expect(data.email).toBe(newUser.email);
      expect(data).toHaveProperty('id');
    });

    it('should handle pagination parameters', async () => {
      const response = await fetch('/api/users?page=1&limit=2', {
        headers: authHeaders,
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data.pagination.page).toBe(1);
      expect(data.pagination.limit).toBe(2);
      expect(data.data.length).toBeLessThanOrEqual(2);
    });
  });

  describe('Company Management Endpoints', () => {
    const authToken = 'mock-jwt-token-dev-admin';
    const authHeaders = { Authorization: `Bearer ${authToken}` };

    it('should fetch companies list', async () => {
      const response = await fetch('/api/companies', {
        headers: authHeaders,
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data).toHaveProperty('data');
      expect(Array.isArray(data.data)).toBe(true);
    });

    it('should fetch active companies only', async () => {
      const response = await fetch('/api/companies/active', {
        headers: authHeaders,
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(Array.isArray(data)).toBe(true);
      data.forEach((company: any) => {
        expect(company.isActive).toBe(true);
      });
    });
  });

  describe('Application Management Endpoints', () => {
    const authToken = 'mock-jwt-token-dev-admin';
    const authHeaders = { Authorization: `Bearer ${authToken}` };

    it('should fetch apps list', async () => {
      const response = await fetch('/api/apps', {
        headers: authHeaders,
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data).toHaveProperty('data');
      expect(Array.isArray(data.data)).toBe(true);
    });

    it('should fetch app categories', async () => {
      const response = await fetch('/api/apps/categories', {
        headers: authHeaders,
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(Array.isArray(data)).toBe(true);
    });
  });

  describe('Configuration Endpoints', () => {
    const authToken = 'mock-jwt-token-dev-admin';
    const authHeaders = { Authorization: `Bearer ${authToken}` };

    it('should fetch all configuration', async () => {
      const response = await fetch('/api/config', {
        headers: authHeaders,
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data).toHaveProperty('features');
      expect(data).toHaveProperty('app');
      expect(data).toHaveProperty('system');
      expect(data).toHaveProperty('environment');
    });

    it('should fetch feature flags', async () => {
      const response = await fetch('/api/config/features', {
        headers: authHeaders,
      });

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data).toHaveProperty('enableAnalytics');
      expect(data).toHaveProperty('enableNotifications');
    });

    it('should handle health check', async () => {
      const response = await fetch('/api/config/health');

      expect(response.ok).toBe(true);
      const data = await response.json();
      expect(data).toHaveProperty('status');
      expect(data).toHaveProperty('timestamp');
      expect(data.status).toBe('healthy');
    });
  });

  describe('Error Handling', () => {
    it('should handle unauthorized requests', async () => {
      const response = await fetch('/api/users');

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data).toHaveProperty('error');
    });

    it('should handle invalid token', async () => {
      const response = await fetch('/api/users', {
        headers: { Authorization: 'Bearer invalid-token' },
      });

      expect(response.status).toBe(401);
    });

    it('should handle not found resources', async () => {
      const authToken = 'mock-jwt-token-dev-admin';
      const response = await fetch('/api/users/nonexistent-id', {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(404);
    });
  });

  describe('Scenarios', () => {
    it('should apply error testing scenario', async () => {
      applyScenario('errorTesting');
      
      // With error testing scenario, some requests should fail
      // This is probabilistic, so we'll just verify the scenario was applied
      expect(true).toBe(true); // Placeholder - in real tests you'd check error rates
    });

    it('should apply fast testing scenario', async () => {
      applyScenario('fastTesting');
      
      // Fast testing should have minimal delays
      const start = Date.now();
      const response = await fetch('/api/config/health');
      const duration = Date.now() - start;
      
      expect(response.ok).toBe(true);
      expect(duration).toBeLessThan(100); // Should be very fast
    });
  });
});
