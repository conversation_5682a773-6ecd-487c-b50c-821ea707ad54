# MSW Infrastructure Migration Guide

This guide helps you migrate from the old mock data approach to the new MSW (Mock Service Worker) infrastructure.

## 🔄 What Changed

### Before (Old Approach)
- Mock data was scattered across stores and components
- Hardcoded data in `src/data/mockApps.ts`, `src/stores/companyStore.ts`, etc.
- No realistic API simulation
- Limited testing capabilities

### After (New MSW Infrastructure)
- Centralized mock data in `src/mocks/data/`
- Realistic API endpoints with proper HTTP methods
- Advanced features like pagination, search, filtering
- Comprehensive testing scenarios
- Environment-based configuration

## 📋 Migration Steps

### 1. Update Store Usage

#### Company Store
**Before:**
```typescript
import { useCompanyStore } from '../stores/companyStore';

const { companies, addCompany } = useCompanyStore();
// Companies were loaded from hardcoded mock data
```

**After:**
```typescript
import { useCompanyStore } from '../stores/companyStore';

const { companies, fetchCompanies, createCompany, isLoading } = useCompanyStore();

// Fetch companies from API
useEffect(() => {
  fetchCompanies();
}, [fetchCompanies]);

// Create company via API
const handleCreateCompany = async (companyData) => {
  const newCompany = await createCompany(companyData);
  if (newCompany) {
    // Company created successfully
  }
};
```

#### App Data
**Before:**
```typescript
import { mockApps, getAppById } from '../data/mockApps';

const apps = mockApps;
const app = getAppById('1');
```

**After:**
```typescript
import { useAppsDataStore } from '../stores/appsDataStore';

const { apps, fetchApps, getAppById } = useAppsDataStore();

// Fetch apps from API
useEffect(() => {
  fetchApps();
}, [fetchApps]);

const app = getAppById('1');
```

### 2. Update API Calls

#### Authentication
**Before:**
```typescript
// Direct mock data access
const user = mockUsers.find(u => u.email === email);
```

**After:**
```typescript
// Real API call (mocked by MSW)
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password }),
});
const { user, token } = await response.json();
```

#### Data Fetching
**Before:**
```typescript
// Immediate data access
const users = mockUsers;
```

**After:**
```typescript
// Async API call with proper error handling
const fetchUsers = async () => {
  try {
    const response = await fetch('/api/users', {
      headers: { Authorization: `Bearer ${token}` },
    });
    if (!response.ok) throw new Error('Failed to fetch users');
    const result = await response.json();
    return result.data; // Paginated response
  } catch (error) {
    console.error('Error fetching users:', error);
  }
};
```

### 3. Update Components

#### Dashboard Component
**Before:**
```typescript
import { mockApps } from '../../data/mockApps';

const Dashboard = () => {
  const apps = mockApps; // Direct import
  // ...
};
```

**After:**
```typescript
import { useAppsDataStore } from '../../stores/appsDataStore';

const Dashboard = () => {
  const { apps, fetchApps, isLoading } = useAppsDataStore();
  
  useEffect(() => {
    fetchApps();
  }, [fetchApps]);

  if (isLoading) return <LoadingSpinner />;
  // ...
};
```

### 4. Update Tests

#### Before
```typescript
import { mockUsers } from '../data/mockUsers';

test('should display users', () => {
  // Test with hardcoded mock data
  render(<UserList users={mockUsers} />);
});
```

#### After
```typescript
import { applyScenario } from '../mocks/scenarios';
import { server } from '../mocks/server';

beforeEach(() => {
  applyScenario('fastTesting');
});

test('should display users', async () => {
  render(<UserList />);
  
  // Wait for API call to complete
  await waitFor(() => {
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });
});
```

## 🔧 New Features Available

### 1. Realistic API Simulation
```typescript
// Pagination
const response = await fetch('/api/users?page=2&limit=10');

// Search
const response = await fetch('/api/users?search=john');

// Filtering
const response = await fetch('/api/users?role=admin&status=active');

// Sorting
const response = await fetch('/api/users?sortBy=name&sortOrder=asc');
```

### 2. Error Handling
```typescript
// MSW can simulate various error scenarios
try {
  const response = await fetch('/api/users');
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  const data = await response.json();
} catch (error) {
  // Handle network errors, timeouts, etc.
  console.error('API Error:', error);
}
```

### 3. Permission-Based Access
```typescript
// Endpoints now validate permissions
const response = await fetch('/api/admin/users', {
  headers: { Authorization: `Bearer ${adminToken}` },
});

if (response.status === 403) {
  // Handle insufficient permissions
}
```

### 4. Development Scenarios
```typescript
// Switch to different testing scenarios
window.mswScenarios.apply('errorTesting'); // High error rate
window.mswScenarios.apply('slowNetwork');  // Slow responses
window.mswScenarios.apply('fastTesting');  // No delays
```

## ⚠️ Breaking Changes

### 1. Async Data Loading
All data is now loaded asynchronously. Components must handle loading states:

```typescript
const { data, isLoading, error } = useDataStore();

if (isLoading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
return <DataComponent data={data} />;
```

### 2. Authentication Required
Most endpoints now require authentication:

```typescript
// Must include auth token
const response = await fetch('/api/users', {
  headers: { Authorization: `Bearer ${token}` },
});
```

### 3. Paginated Responses
List endpoints return paginated data:

```typescript
const response = await fetch('/api/users');
const { data, pagination } = await response.json();

// data: User[]
// pagination: { page, limit, total, totalPages, hasNext, hasPrev }
```

## 🧪 Testing Migration

### Unit Tests
```typescript
// Before
import { mockUsers } from '../data/mockUsers';

// After
import { users } from '../mocks/data/users';
```

### Integration Tests
```typescript
// Before
// No API simulation

// After
import { setupServer } from 'msw/node';
import { handlers } from '../mocks/handlers';

const server = setupServer(...handlers);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

## 📚 Resources

- [MSW Documentation](https://mswjs.io/)
- [MSW Infrastructure README](./src/mocks/README.md)
- [API Endpoints Documentation](./src/mocks/README.md#-api-endpoints)
- [Testing Guide](./src/mocks/README.md#-testing)

## 🆘 Troubleshooting

### Common Issues

1. **Data not loading**: Check if `fetchData()` is called in `useEffect`
2. **Authentication errors**: Ensure auth token is included in requests
3. **Type errors**: Update interfaces to match new API response format
4. **Tests failing**: Apply appropriate MSW scenario for testing

### Getting Help

1. Check the browser console for MSW logs
2. Use `window.mswScenarios` in browser console for debugging
3. Review the comprehensive test file: `src/mocks/__tests__/msw-setup.test.ts`
4. Check the MSW README for detailed documentation

## ✅ Migration Checklist

- [ ] Update all store usage to use new API methods
- [ ] Add loading states to components
- [ ] Update error handling
- [ ] Migrate tests to use MSW
- [ ] Remove old mock data imports
- [ ] Test authentication flows
- [ ] Verify pagination works correctly
- [ ] Test error scenarios
- [ ] Update documentation
