// Application management handlers for MSW
import { http, HttpResponse } from 'msw';
import { 
  apps, 
  getAppById,
  getActiveApps,
  getAppsByCategory,
  getAppCategories,
  searchApps,
  type AppData 
} from '../data/apps';
import { getUserById, type User } from '../data/users';
import { 
  delay, 
  randomDelay, 
  simulateError, 
  commonErrors,
  successResponse,
  errorResponse,
  notFoundResponse,
  unauthorizedResponse,
  extractBearerToken,
  validateToken,
  paginate,
  fuzzySearch,
  sortData,
  filterData,
  parseQueryParams,
  generateId,
  generateTimestamp,
  type SortDirection,
  type FilterCondition
} from '../utils';

// Mutable copy of apps for CRUD operations
let mutableApps = [...apps];

// Helper to check permissions
const hasPermission = (user: User, permission: string): boolean => {
  return user.permissions.includes(permission) || 
         user.permissions.includes('admin') ||
         user.permissions.includes('manage_apps');
};

// Helper to get authenticated user
const getAuthenticatedUser = async (authHeader: string | null): Promise<User | null> => {
  const token = extractBearerToken(authHeader);
  if (!token) return null;
  
  const userId = validateToken(token);
  if (!userId) return null;
  
  return getUserById(userId) || null;
};

export const appHandlers = [
  // Get all apps with pagination, search, and filtering
  http.get('/api/apps', async ({ request }) => {
    await randomDelay(200, 500);
    
    // Simulate occasional errors
    const error = simulateError([commonErrors.serverError]);
    if (error) return error;
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const url = new URL(request.url);
    const params = parseQueryParams(url);
    
    let filteredApps = [...mutableApps];
    
    // Apply search
    if (params.search) {
      filteredApps = fuzzySearch(filteredApps, params.search, ['title', 'description', 'category']);
    }
    
    // Apply filters
    const filters: FilterCondition<AppData>[] = [];
    if (params.category) {
      filters.push({ field: 'category', operator: 'eq', value: params.category });
    }
    if (params.isActive !== undefined) {
      filters.push({ field: 'isActive', operator: 'eq', value: params.isActive });
    }
    
    if (filters.length > 0) {
      filteredApps = filterData(filteredApps, filters);
    }
    
    // Apply sorting
    if (params.sortBy) {
      const direction: SortDirection = params.sortOrder === 'desc' ? 'desc' : 'asc';
      filteredApps = sortData(filteredApps, params.sortBy as keyof AppData, direction);
    }
    
    // Apply pagination
    const paginatedResult = paginate(filteredApps, {
      page: params.page,
      limit: params.limit,
    });
    
    return successResponse(paginatedResult);
  }),

  // Get active apps only
  http.get('/api/apps/active', async ({ request }) => {
    await delay(150);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const activeApps = mutableApps.filter(app => app.isActive !== false);
    return successResponse(activeApps);
  }),

  // Get app categories
  http.get('/api/apps/categories', async ({ request }) => {
    await delay(100);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const categories = getAppCategories();
    return successResponse(categories);
  }),

  // Get single app by ID
  http.get('/api/apps/:id', async ({ params, request }) => {
    await delay(100);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const app = mutableApps.find(a => a.id === params.id);
    if (!app) {
      return notFoundResponse('App');
    }
    
    return successResponse(app);
  }),

  // Get app view content
  http.get('/api/apps/:id/views/:viewId', async ({ params, request }) => {
    await delay(150);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const app = mutableApps.find(a => a.id === params.id);
    if (!app) {
      return notFoundResponse('App');
    }
    
    const view = app.views?.[params.viewId as string];
    if (!view) {
      return notFoundResponse('View');
    }
    
    // Check view-specific permissions
    if (view.permissions) {
      const hasViewPermission = view.permissions.some(permission => 
        hasPermission(currentUser, permission)
      );
      if (!hasViewPermission) {
        return HttpResponse.json({ error: 'Insufficient permissions for this view' }, { status: 403 });
      }
    }
    
    return successResponse(view);
  }),

  // Create new app
  http.post('/api/apps', async ({ request }) => {
    await randomDelay(800, 1200);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'write') && !hasPermission(currentUser, 'manage_apps')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    try {
      const appData = await request.json() as Partial<AppData>;
      
      // Validate required fields
      if (!appData.title || !appData.description) {
        return errorResponse('Title and description are required', 400);
      }
      
      const newApp: AppData = {
        id: generateId(),
        title: appData.title,
        description: appData.description,
        icon: appData.icon || '📱',
        color: appData.color || '#6b7280',
        gradient: appData.gradient || false,
        category: appData.category || 'General',
        isActive: appData.isActive !== false,
        version: appData.version || '1.0.0',
        createdAt: generateTimestamp(),
        updatedAt: generateTimestamp(),
        permissions: appData.permissions || ['read'],
        navLinks: appData.navLinks || [],
        views: appData.views || {},
        settings: appData.settings || {
          enableNotifications: true,
          enableAnalytics: false,
          maxUsers: 50,
          features: [],
        },
      };
      
      mutableApps.push(newApp);
      
      return successResponse(newApp, 201);
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Update app
  http.put('/api/apps/:id', async ({ params, request }) => {
    await delay(600);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'write') && !hasPermission(currentUser, 'manage_apps')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const appIndex = mutableApps.findIndex(a => a.id === params.id);
    if (appIndex === -1) {
      return notFoundResponse('App');
    }
    
    try {
      const updateData = await request.json() as Partial<AppData>;
      const existingApp = mutableApps[appIndex];
      
      const updatedApp: AppData = {
        ...existingApp,
        ...updateData,
        id: existingApp.id, // Prevent ID changes
        createdAt: existingApp.createdAt, // Prevent creation date changes
        updatedAt: generateTimestamp(),
      };
      
      mutableApps[appIndex] = updatedApp;
      
      return successResponse(updatedApp);
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Delete app
  http.delete('/api/apps/:id', async ({ params, request }) => {
    await delay(400);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'delete') && !hasPermission(currentUser, 'manage_apps')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const appIndex = mutableApps.findIndex(a => a.id === params.id);
    if (appIndex === -1) {
      return notFoundResponse('App');
    }
    
    mutableApps.splice(appIndex, 1);
    
    return successResponse({ message: 'App deleted successfully' });
  }),

  // Get app statistics
  http.get('/api/apps/stats', async ({ request }) => {
    await delay(200);
    
    const authHeader = request.headers.get('Authorization');
    const currentUser = await getAuthenticatedUser(authHeader);
    
    if (!currentUser) {
      return unauthorizedResponse();
    }
    
    if (!hasPermission(currentUser, 'read')) {
      return HttpResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    
    const stats = {
      total: mutableApps.length,
      active: mutableApps.filter(a => a.isActive !== false).length,
      inactive: mutableApps.filter(a => a.isActive === false).length,
      byCategory: mutableApps.reduce((acc, app) => {
        if (app.category) {
          acc[app.category] = (acc[app.category] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>),
    };
    
    return successResponse(stats);
  }),
];
