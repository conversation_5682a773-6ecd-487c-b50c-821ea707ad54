// Authentication handlers for MSW
import { http, HttpResponse } from 'msw';
import { 
  allUsers, 
  validateCredentials, 
  getUserById,
  type User 
} from '../data/users';
import { 
  delay, 
  randomDelay, 
  simulateError, 
  commonErrors,
  successResponse,
  errorResponse,
  unauthorizedResponse,
  extractBearerToken,
  validateToken,
  generateTimestamp
} from '../utils';

// In-memory session storage for demo purposes
const activeSessions = new Map<string, { userId: string; expiresAt: number }>();

// Helper to generate mock JWT token
const generateToken = (userId: string): string => {
  return `mock-jwt-token-${userId}`;
};

// Helper to create user response
const createUserResponse = (user: User) => ({
  id: user.id,
  name: user.name,
  email: user.email,
  role: user.role,
  avatar: user.avatar,
  permissions: user.permissions,
  status: user.status,
  preferences: user.preferences,
  lastLogin: generateTimestamp(),
});

export const authHandlers = [
  // Login endpoint
  http.post('/api/auth/login', async ({ request }) => {
    await randomDelay(800, 1500); // Simulate realistic login delay
    
    // Simulate occasional errors
    const error = simulateError([commonErrors.serverError, commonErrors.badRequest]);
    if (error) return error;
    
    try {
      const credentials = await request.json() as { email: string; password: string };
      
      if (!credentials.email || !credentials.password) {
        return errorResponse('Email and password are required', 400);
      }
      
      const user = validateCredentials(credentials.email, credentials.password);
      
      if (!user) {
        await delay(1000); // Prevent timing attacks
        return errorResponse('Invalid credentials', 401);
      }
      
      if (user.status !== 'active') {
        return errorResponse('Account is not active', 403);
      }
      
      const token = generateToken(user.id);
      const expiresIn = 3600; // 1 hour
      const expiresAt = Date.now() + (expiresIn * 1000);
      
      // Store session
      activeSessions.set(token, { userId: user.id, expiresAt });
      
      return successResponse({
        user: createUserResponse(user),
        token,
        expiresIn,
        tokenType: 'Bearer',
      });
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Development login endpoint
  http.post('/api/auth/dev-login', async ({ request }) => {
    await delay(300);
    
    try {
      const { userId } = await request.json() as { userId: string };
      
      if (!userId) {
        return errorResponse('User ID is required', 400);
      }
      
      const user = getUserById(userId);
      if (!user) {
        return errorResponse('User not found', 404);
      }
      
      const token = generateToken(user.id);
      const expiresIn = 3600;
      const expiresAt = Date.now() + (expiresIn * 1000);
      
      activeSessions.set(token, { userId: user.id, expiresAt });
      
      return successResponse({
        user: createUserResponse(user),
        token,
        expiresIn,
        tokenType: 'Bearer',
        isDev: true,
      });
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Logout endpoint
  http.post('/api/auth/logout', async ({ request }) => {
    await delay(200);
    
    const authHeader = request.headers.get('Authorization');
    const token = extractBearerToken(authHeader);
    
    if (token) {
      activeSessions.delete(token);
    }
    
    return successResponse({ message: 'Logged out successfully' });
  }),

  // Get current user profile
  http.get('/api/auth/me', async ({ request }) => {
    await delay(300);
    
    const authHeader = request.headers.get('Authorization');
    const token = extractBearerToken(authHeader);
    
    if (!token) {
      return unauthorizedResponse('Authorization header required');
    }
    
    const userId = validateToken(token);
    if (!userId) {
      return unauthorizedResponse('Invalid token format');
    }
    
    // Check if session exists and is valid
    const session = activeSessions.get(token);
    if (!session || session.expiresAt < Date.now()) {
      activeSessions.delete(token);
      return unauthorizedResponse('Token expired');
    }
    
    const user = getUserById(userId);
    if (!user) {
      return unauthorizedResponse('User not found');
    }
    
    if (user.status !== 'active') {
      return unauthorizedResponse('Account is not active');
    }
    
    return successResponse({
      user: createUserResponse(user),
    });
  }),

  // Refresh token endpoint
  http.post('/api/auth/refresh', async ({ request }) => {
    await delay(200);
    
    const authHeader = request.headers.get('Authorization');
    const token = extractBearerToken(authHeader);
    
    if (!token) {
      return unauthorizedResponse('Authorization header required');
    }
    
    const userId = validateToken(token);
    if (!userId) {
      return unauthorizedResponse('Invalid token format');
    }
    
    const session = activeSessions.get(token);
    if (!session) {
      return unauthorizedResponse('Session not found');
    }
    
    const user = getUserById(userId);
    if (!user || user.status !== 'active') {
      activeSessions.delete(token);
      return unauthorizedResponse('User not found or inactive');
    }
    
    // Generate new token
    const newToken = generateToken(user.id);
    const expiresIn = 3600;
    const expiresAt = Date.now() + (expiresIn * 1000);
    
    // Remove old session and create new one
    activeSessions.delete(token);
    activeSessions.set(newToken, { userId: user.id, expiresAt });
    
    return successResponse({
      token: newToken,
      expiresIn,
      tokenType: 'Bearer',
    });
  }),

  // Password reset request
  http.post('/api/auth/forgot-password', async ({ request }) => {
    await randomDelay(1000, 2000);
    
    try {
      const { email } = await request.json() as { email: string };
      
      if (!email) {
        return errorResponse('Email is required', 400);
      }
      
      // Always return success for security (don't reveal if email exists)
      return successResponse({
        message: 'If an account with that email exists, a password reset link has been sent.',
      });
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Password reset
  http.post('/api/auth/reset-password', async ({ request }) => {
    await delay(800);
    
    try {
      const { token, password } = await request.json() as { 
        token: string; 
        password: string; 
      };
      
      if (!token || !password) {
        return errorResponse('Token and password are required', 400);
      }
      
      if (password.length < 8) {
        return errorResponse('Password must be at least 8 characters long', 400);
      }
      
      // In a real app, you'd validate the reset token
      // For demo purposes, we'll just return success
      return successResponse({
        message: 'Password has been reset successfully',
      });
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),

  // Change password
  http.post('/api/auth/change-password', async ({ request }) => {
    await delay(500);
    
    const authHeader = request.headers.get('Authorization');
    const token = extractBearerToken(authHeader);
    
    if (!token) {
      return unauthorizedResponse('Authorization header required');
    }
    
    const userId = validateToken(token);
    if (!userId) {
      return unauthorizedResponse('Invalid token');
    }
    
    try {
      const { currentPassword, newPassword } = await request.json() as {
        currentPassword: string;
        newPassword: string;
      };
      
      if (!currentPassword || !newPassword) {
        return errorResponse('Current password and new password are required', 400);
      }
      
      if (newPassword.length < 8) {
        return errorResponse('New password must be at least 8 characters long', 400);
      }
      
      const user = getUserById(userId);
      if (!user) {
        return unauthorizedResponse('User not found');
      }
      
      // In a real app, you'd validate the current password
      // For demo purposes, we'll just return success
      return successResponse({
        message: 'Password changed successfully',
      });
    } catch (error) {
      return errorResponse('Invalid request format', 400);
    }
  }),
];

// Cleanup expired sessions periodically
setInterval(() => {
  const now = Date.now();
  for (const [token, session] of activeSessions.entries()) {
    if (session.expiresAt < now) {
      activeSessions.delete(token);
    }
  }
}, 60000); // Clean up every minute
